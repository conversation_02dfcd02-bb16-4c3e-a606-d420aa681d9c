# .env.example
# This file is a template for your environment variables.
# Copy this file to .env and fill in the values.
# Do NOT commit .env to version control.

# Backstage Environment Variables
# This file contains sensitive configuration values and should not be committed to version control


# # URL Configuration
# BACKSTAGE_APP_BASE_URL=http://localhost:7007
# BACKSTAGE_CORS_ORIGIN=http://localhost:3000
# BACKSTAGE_EXPERIMENTAL_EXTRA_ALLOWED_ORIGINS=

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=backstage
POSTGRES_PASSWORD=backstage

# GitHub Integration
# Personal Access Token (PAT) with appropriate scopes (e.g., repo)
# See Backstage documentation for required scopes.
GITHUB_TOKEN=your_github_personal_access_token

# GitHub OAuth Configuration
# Register an OAuth app on GitHub and fill these values.
# The callback URL for your GitHub OAuth app should be:
# <BAC<PERSON><PERSON>GE_APP_BASE_URL>/api/auth/github/handler/frame
AUTH_GITHUB_CLIENT_ID=your_github_oauth_client_id
AUTH_GITHUB_CLIENT_SECRET=your_github_oauth_client_secret

# Environment Configuration
# This variable determines the environment in which Backstage is running
# Options: development, production
NODE_ENV=development